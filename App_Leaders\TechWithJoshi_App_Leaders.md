**Registration Page**

| Field Name | Input Type | Placeholder/Description | Notes |
| :---- | ----- | ----- | ----- |
| Company Name | Text Input | Enter the name of the institute/organisation | Required field. |
| Organisation Type | Dropdown | Select the type of organisation | Options: \- Corporate (CSR / ESG / Sustainability Division) \- Foundation (Corporate / Family / Philanthropic) \- Social Enterprise (Impact-led Business) \- NGO / Non-profit Organisation \- Impact Investment / ESG Fund \- Advisory / Consulting Firm (CSR / ESG / Sustainability) \- Other (with text input for “Please specify”) |
| Designation | Dropdown/Radio | Select your designation. | Options: \- Chief Sustainability Officer (CSO) \- Head of CSR/ESG \- Director, Sustainability \- Manager, CSR Programs \- ESG Analyst \- Social Impact Specialist \- Development Sector Lead \- Program Manager (Social Development) \- Other (with a text input field for "Please specify") |
| Theme | Multiselect/All | Specify the name of the course or program. (e.g., Post Graduate Diploma in Business Management) | Options (Social Sector Themes): \- Health and Nutrition \- Education & Skill Development \- Environment & Sustainability \- Gender Equality & Social Inclusion \- Livelihoods & Economic Empowerment \- Water & Sanitation \- Other (with a text input field for "Please specify") |
| Terms & Conditions | Checkbox | Confirm agreement to the terms and conditions. | Link to full T\&C document should be available. Required for submission. |
| Buttons | Button | Preview, Post | Preview: Allows user to review entered data before final submission. Post: Submits the data. |

**Splash Sceen:**

| Field Name | Input Type | Placeholder/Description | Notes |
| :---- | :---- | :---- | :---- |
| Logo | Impact Leaders App |  |  |
| Name | Text Input | Enter your first name. | Required field. |
| Email Address | Text Input (Email) | Provide your email address. | Will not be displayed publicly. Required field. |
| Contact | Text Input (Phone) | Provide your mobile number. | Will not be displayed publicly. Optional field. |
| Referral Code | login |  |  |

**Home Screen**

| Section | Element | Type | Placeholder/Text | Notes |
| :---- | ----- | ----- | ----- | ----- |
| Header | Time \+ Status Icons | System UI | 9:41, Battery %, Network Icons | Standard iOS/Android status bar |
|  | Profile Icon | Icon/Button | \[User Avatar\] | Tap to access user profile or settings |
| Latest Posts | Section Title | Text (H1) | Latest Posts | Bold, white text |
|  | Post Preview Card | Card | Recent Community Updates | Includes: user image, background banner, brief snippet |
|  | User Name | Text (sub-header) | e.g., Karthik Iyer | Displayed with green tag/pill |
|  | Post Button 1 | Button | Post an Update | White background, black border |
|  | Post Button 2 | Button | Find a Leader | Same style as above |
| Latest Happenings | Section Title | Text (H2) | Latest Happenings in CSR/ESG | Bold white text |
|  | News/Content Cards | Card Grid (2x2 layout or scrollable) | Impact stories from Impact Box \-2 (Pepository) Any new Post by the user (Linkedin Con) Any new Questions/Answers posted(Q/A section) Changes after 72 hours, the older ones are archived into the repository | Each card includes: • Title (max 2 lines) • Subtext (1–2 lines) • Optional tag/icon/image |
| Bottom Navigation | Navigation Tabs | Icon Buttons (5) | Home, Directory, Messages, Q\&A, Resources | Fixed nav bar with icon \+ label per tab |
|  | Center Action Button | Floating Button | \[↑\] or \[+\] | Circular button floating |

**Hamburger Menu:**

| Section | Element Type | Placeholder/Text | Notes |
| :---- | :---- | :---- | :---- |
| Header | User Info | e.g., "Harshita Bisht" \+ Profile Image | Shows user name and avatar at the top. |
|  | View Profile | Button/Text Link "View Profile" | Navigates to full profile page. |
|  | Divider | Line Divider | Separates user info from menu items. |
| Menu Items | Home | Text \+ Icon Home | Takes user to homepage or latest posts screen. |
|  | Directory | Text \+ Icon Directory | Opens networking directory. |
|  | Messages | Text \+ Icon Messages | Direct messaging section. |
|  | Profile | Text \+ Icon | Goes to personal Profile section. |
|  | Referral | Text \+ Icon | Referal code |
|  | Saved Items | Text \+ Icon Saved Posts | View saved posts, bookmarks. |
|  | Settings | Text \+ Icon Settings | Preferences, notifications, app settings. |
|  | Help & Support | Text \+ Icon Help & Support | FAQs, contact form, feedback option. |
|  | Divider | Line Divider | Separates settings from logout. |
|  | Logout | Text \+ Icon Logout | Logs the user out of the app. |
| Footer | Version Info | Text (small) "v1.0.0 © 2025 ImpactLeader" | Displays the app version and copyright information. |

**Directory:**

| Section | Element | Type | Placeholder/Text | Notes |
| :---- | ----- | ----- | ----- | ----- |
| Header | Page Title | Text (H1) | "Directory" | Centered, bold, white text |
|  | Search Bar | Input Field | "Search by name, org, theme..." | Top input with magnifying glass icon |
|  | Filter Button | Icon/Button | \[Filter Icon\] | Opens modal with filters (sector, region, theme, etc.) |
| Filter Modal | Filter Categories | Multi-select | Sector, Geography, Theme, Org Type | Selectable checkboxes/dropdowns |
| Sub-section | Suggested Searches | Tags/Chips | \- Health & Well-being \- Education & Skill Development \- Environment & Climate Action \- Gender Equality & Social Inclusion \- Livelihoods & Economic Empowerment \- Water & Sanitation | Optional: quick filters as scrollable tags |
| Directory Cards | Profile Card | Card (2x layout) | Name: e.g., “Anjali Mehta” Org: Piramal Foundation- Health | Includes avatar, name, org name, sector tag, Theme(That they have filled in the registration page , once they registered on page |
|  |  |  |  |  |
|  | Org Tag | Label/Pill | e.g., “Philanthropic Foundation” | Distinguishes NGOs, FOs, CSR, Think Tanks |
|  | View Profile Button | Icon/Text | “View Profile” | Opens full profile with org, themes, contact info |
|  |  |  |  |  |
| Bottom Navigation | Nav Tabs | Icon Buttons (5) | Home, Directory (highlighted), Messages, Q\&A, Resources | Fixed at the bottom |
| Floating Action | Add Contact / Add Org | Floating Button | \[+\] | (Admin only) Add new pro |

**Messages(Socket):**

| Field | Element | Type | Placeholder/Text | Notes |
| ----- | ----- | ----- | ----- | ----- |
| Header | Page Title | Text (H1) | "Messages" | Bold, white text |
|  | Search Bar | Input Field | "Search by name or organization..." | Allows search through message history or contacts |
|  | Filter Icon | Icon/Button | \[Filter\] | Opens filter modal (e.g., Unread, Starred, Groups) |
| Chat List | Chat Card | Card/List Item | \[Profile Pic\] \[Name\] \[Last Message Snip\] | List of recent conversations |
|  | Timestamp | Text | e.g., "2h ago", "Yesterday" | Last message time |
|  | Notification Badge | Badge | Red dot with count | For unread messages |
| Chat Window (on tap) | Header | Name \+ Status | e.g., "Amit Shah • Online" | Shows user name and active/offline status |
|  | Chat Thread | Messages | Left (Other), Right (You) | Message bubbles; timestamps optional |
|  | Input Bar | Text Input | "Type your message..." | Fixed bottom area |
|  | Attachment Icon | Icon/Button | \[Paperclip\] | Tap to send document or image |
|  | Send Button | Icon/Button | \[Send Arrow\] | Sends message |
| Empty State | Message | Text \+ Icon | "No messages yet. Start a new chat\!" | Displayed when no chats exist |
| FAB (Optional) | New Chat Button | Floating Icon | \[+\] or \[Message Icon\] | Opens directory or search to start a new chat |

**Q\&A:**

| Section | Element | Type | Placeholder/Text | Notes |
| :---- | ----- | ----- | ----- | ----- |
| Header | Page Title | Text (H1) | "Community Q\&A" | Bold, white text |
|  | Ask Question Button | Button | "+ Ask a Question" | Prominent button, opens Ask Modal/Form |
|  | Search Bar | Input Field | "Search questions by topic or keyword..." | For filtering/searching existing questions |
|  | Filter Icon | Icon/Button | \[Filter Icon\] | Opens modal (e.g., Latest, Popular, Unanswered, My Questions) |
| Question Feed | Question Card | Card/List Item | "How to implement a CSR program in rural areas?" | Displays title \+ tags \+ stats (answers/views) |
|  | Asked by Info | Text (caption) | "Asked by Priya Sinha • 2 days ago" | User name and timestamp |
|  | Tag Chips | Tags/Pills | e.g., \#Education, \#Impact, \#Compliance | Clickable for filtering |
|  | Answer Count | Icon/Text Combo | "3 Answers" | Indicates how many answers it has |
|  | View Question Button | Tap to expand | \[Tap to open full thread\] | Opens detail view with answers |
| Question Detail View | Question Title | Text (H2) | Full question headline |  |
|  | Question Description | Text (Body) | Full question content | Optional longer context |
|  | Post Answer Button | Button | "Post an Answer" | Below the question and answers |
|  | Answer Cards | Card/List | Answers from users | Each card includes name, timestamp, upvote, reply |
|  | Upvote Button | Icon/Button | \[Up arrow\] \+ Count | Users can upvote best answers |
|  | Comments (Optional) | Nested Replies | Comment under answer | For follow-up discussion |
| Ask Question Modal | Question Input | Text Field | "Write your question here..." | Large input area |
|  | Tags | Tag Selector | e.g., Select up to 3 tags | Optional but helpful |
|  | Submit Button | Button | "Post Question" | Bottom of modal |
| Empty State | No Questions Message | Text \+ Icon | "No questions yet. Be the first to ask\!" | Friendly nudge |

**Resource Repository:**

| Section | Element | Type | Placeholder/Text | Notes |
| :---- | ----- | ----- | ----- | ----- |
| Header | Title | Text (H1) | "Knowledge Hub" or "Resources" | Bold white or primary-colored text |
|  | Search Bar | Input Field | "Search resources, reports, policies…" | Optional voice input/mic icon |
| Category Tabs | Tabs | Button Tabs | Reports,Policies, Case Studies, Templates | Horizontal scrollable tab layout for easy navigation |
| Filter Button | Icon/Button | \[Filter Icon\] | Opens filter modal | Top-right corner |
| Filter Modal | Filter Categories | Multi-select | Type (PDF/Video/Link), Theme, Region, Org Type | Checkboxes or dropdowns |
| Featured Resource | Card (Top Highlight) | Banner Card | "Featured: CSR Policy report" | Large banner card with image thumbnail and brief description |
| Resource Cards Grid | Card List/Grid (2x2) | Title \+ Type \+ Tag | e.g., "ESG Annual Report 2023", \[PDF\] \[Environment\] | Clickable cards with small preview and downloadable link |
|  | Resource Tag | Label | PDF / Video / Link | Tag shows format |
|  | Theme Tag | Label | e.g., Environment, Education, Livelihood | Use colored pills |
|  | View/Download Button | Button (Icon/Text) | \[Eye Icon\] View / \[Download Icon\] Download | Option to open or save the resource |
| Bottom Navigation | Navigation Tabs | Icon Buttons (5) | Home, Directory, Messages, Q\&A, Resources | Resources tab highlighted when in this section |
| Center Action Button | Floating Button | \[+\] Add Resource | Visible only to admins or contributors | Opens upload modal |
| Upload Modal | Form Fields | Input \+ File Upload | Title, Description, File Upload, Theme Tag, Format | For uploading new |

